# Chat History and Conversation APIs - Updated with Count Parameter

## Overview

Both the `/chat_history` and `/conversation` endpoints have been updated to support an optional `count` parameter that allows you to limit the number of records returned.

## API Endpoints

```
GET /chat_history
GET /conversation
```

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `from_id` | int | Yes | The ID of the sender |
| `chat_id` | int | Yes | The ID of the chat/recipient |
| `count` | int | No | Number of recent records to retrieve. If not provided, all records are returned. |

## Usage Examples

### 1. Get All Chat History (Existing Behavior)

```bash
curl -X GET "http://localhost:8000/chat_history?from_id=123&chat_id=456" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This maintains backward compatibility - existing API calls continue to work unchanged.

### 2. Get Last 10 Chat History Records

```bash
curl -X GET "http://localhost:8000/chat_history?from_id=123&chat_id=456&count=10" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This returns only the 10 most recent chat history records.

### 3. Get Last 5 Chat History Records

```bash
curl -X GET "http://localhost:8000/chat_history?from_id=123&chat_id=456&count=5" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This returns only the 5 most recent chat history records.

### 4. Get All Conversation Records (Existing Behavior)

```bash
curl -X GET "http://localhost:8000/conversation?from_id=123&chat_id=456" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This maintains backward compatibility for conversation endpoint.

### 5. Get Last 10 Conversation Records

```bash
curl -X GET "http://localhost:8000/conversation?from_id=123&chat_id=456&count=10" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This returns only the 10 most recent conversation records.

### 6. Get Last 3 Conversation Records

```bash
curl -X GET "http://localhost:8000/conversation?from_id=123&chat_id=456&count=3" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

This returns only the 3 most recent conversation records.

## Response Format

The response format remains the same for both endpoints:

**Chat History Response:**
```json
{
  "chat_history": [
    ["Chat History:\n\n[message content]\n"]
  ]
}
```

**Conversation Response:**
```json
{
  "conversation": [
    "[User Name]: message content\n[Other User]: response content"
  ]
}
```

## Implementation Details

### Caching Behavior

- Cache keys now include the `count` parameter to ensure correct caching behavior
- Different `count` values are cached separately
- Cache TTL remains 30 minutes (1800 seconds)

### SQL Query Changes

**For Chat History (`/chat_history`):**
When `count` is specified:
- Uses a subquery with `LIMIT` to get the last N messages
- Orders by timestamp DESC to get the most recent messages first

When `count` is not specified (default):
- Uses the original query to maintain existing behavior
- Returns all chat history records

**For Conversation (`/conversation`):**
When `count` is specified:
- Uses a subquery with `LIMIT` to get the last N conversation messages
- Orders by timestamp DESC in subquery, then ASC in outer query to maintain chronological order
- Preserves the conversation flow format

When `count` is not specified (default):
- Uses the original query to maintain existing behavior
- Returns all conversation records in chronological order

### Backward Compatibility

- Existing API calls without the `count` parameter continue to work unchanged
- No breaking changes to the response format
- Cache invalidation works correctly for both old and new cache keys

## Performance Considerations

- Using the `count` parameter can improve performance for large chat histories
- Smaller result sets reduce memory usage and network transfer time
- Caching ensures repeated requests with the same parameters are served quickly
